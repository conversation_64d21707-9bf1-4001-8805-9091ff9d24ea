# Project Structure

## Final Project Structure

```text
viding-static/
├── 📁 app/                          # Main application package
│   ├── __init__.py                  # Package initialization & app factory export
│   ├── main.py                      # Application factory & configuration
│   ├── 📁 core/                     # Core application components
│   │   ├── __init__.py              # Core module exports
│   │   ├── config.py                # Environment-specific configurations
│   │   ├── errors.py                # Custom exception classes
│   │   ├── logging.py               # Logging configuration & utilities
│   │   └── middleware.py            # Request/response middleware
│   ├── 📁 api/                      # Route handlers (Flask Blueprints)
│   │   ├── __init__.py              # API module exports
│   │   ├── main.py                  # Main invitation routes (/, etc.)
│   │   ├── pages.py                 # Page routes (/greeting, /rsvp, etc.)
│   │   └── health.py                # Health check endpoints
│   └── 📁 services/                 # Business logic layer
│       ├── __init__.py              # Services module exports
│       ├── content_service.py       # Content fetching & processing
│       ├── template_service.py      # Template rendering
│       └── url_service.py           # URL manipulation & building
├── 📁 tests/                        # Test suite
│   ├── __init__.py                  # Test package initialization
│   ├── conftest.py                  # Pytest configuration & fixtures
│   ├── test_health.py               # Health endpoint tests
│   └── test_services.py             # Service layer tests
├── 📁 requirements/                 # Dependency management
│   ├── base.txt                     # Base dependencies
│   ├── development.txt              # Development dependencies
│   ├── production.txt               # Production dependencies
│   └── testing.txt                  # Testing dependencies
├── 📁 scripts/                      # Development & utility scripts
│   ├── dev.py                       # Development server script
│   ├── lint.py                      # Code quality & linting script
│   └── test.py                      # Test runner script
├── 📁 .github/workflows/            # CI/CD pipeline
│   └── ci.yml                       # GitHub Actions workflow
├── 📁 logs/                         # Application logs
│   └── app.log                      # Main application log file
├── 📁 htmlcov/                      # Test coverage reports
├── main.py                          # Application entry point
├── pyproject.toml                   # Modern Python project configuration
├── requirements.txt                 # Legacy requirements (points to requirements/)
├── Makefile                         # Development task automation
├── Dockerfile                       # Docker configuration
├── docker-compose.yml               # Docker Compose configuration
├── .env.example                     # Environment variables template
├── README.md                        # Project documentation
├── TROUBLESHOOTING.md               # Troubleshooting guide
├── MIGRATION_SUMMARY.md             # Migration summary
├── PROJECT_STRUCTURE.md             # This file
├── fallback.html                    # Fallback HTML content
└── robots.txt                       # SEO robots file
```

## Architecture Overview

### 🏗️ **Application Factory Pattern**

- **`app/main.py`**: Creates Flask app instances with proper configuration
- **Environment-specific configs**: Development, Production, Testing
- **Modular initialization**: Logging, middleware, blueprints

### 🔧 **Core Components**

- **Configuration**: Environment-based settings with validation
- **Logging**: Structured logging with request tracing
- **Error Handling**: Custom exceptions with proper HTTP responses
- **Middleware**: Security headers, request validation, error handling

### 🌐 **API Layer (Blueprints)**

- **Main Routes**: Primary invitation functionality
- **Page Routes**: Specific page handlers (greeting, RSVP, etc.)
- **Health Routes**: Monitoring and health check endpoints

### 🔄 **Service Layer**

- **Content Service**: External content fetching and processing
- **Template Service**: HTML template rendering and management
- **URL Service**: URL parsing, building, and manipulation

### 🧪 **Testing Framework**

- **pytest**: Modern testing framework with Flask integration
- **Coverage**: Comprehensive test coverage reporting
- **Fixtures**: Reusable test components and configurations

### 📦 **Dependency Management**

- **Structured requirements**: Separate files for different environments
- **pyproject.toml**: Modern Python packaging configuration
- **Development tools**: Linting, formatting, type checking

### 🚀 **Development & Deployment**

- **Make targets**: Common development tasks automation
- **Docker**: Containerized deployment with multi-stage builds
- **CI/CD**: GitHub Actions for automated testing and deployment
- **Scripts**: Development server, testing, and linting utilities

## Key Features

### ✅ **Production Ready**

- Structured logging with JSON format
- Security headers and middleware
- Health check endpoints for monitoring
- Error handling with proper HTTP status codes
- Environment-specific configurations

### ✅ **Developer Friendly**

- Hot reload development server
- Comprehensive test suite with coverage
- Code quality tools (black, flake8, mypy, isort)
- Clear documentation and troubleshooting guides
- Easy setup with Make targets

### ✅ **Maintainable**

- Clean separation of concerns
- Modular architecture with clear boundaries
- Type hints and documentation
- Consistent code style and formatting

### ✅ **Scalable**

- Service layer for business logic
- Blueprint-based routing
- Configurable logging and monitoring
- Docker support for containerized deployment

## Usage Examples

### Development

```bash
# Setup environment
make setup

# Start development server
make dev

# Run tests with coverage
make test

# Run code quality checks
make lint
```

### Production

```bash
# Install production dependencies
pip install -r requirements/production.txt

# Run with gunicorn
gunicorn main:app

# Or use Docker
docker build -t viding-static .
docker run -p 80:80 viding-static
```

### Testing

```bash
# Run all tests
python -m pytest

# Run specific test file
python -m pytest tests/test_health.py

# Run with coverage
python -m pytest --cov=app
```

## Migration Benefits

1. **From Monolithic to Modular**: Single file → organized package structure
2. **From Basic to Comprehensive**: Simple logging → structured logging with tracing
3. **From Manual to Automated**: Manual testing → automated test suite with CI/CD
4. **From Development to Production**: Basic setup → production-ready deployment
5. **From Maintenance to Growth**: Hard to maintain → easy to extend and scale

This structure provides a solid foundation for continued development while maintaining the existing functionality and improving upon it significantly.
