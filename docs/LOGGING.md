# Logging Configuration

This document describes the logging configuration and format used in the Viding Static application.

## Log Format

The application uses a custom logging format called "Viding format" that provides clear, structured log output:

```
2025-05-29 09:50:22.002 | DEBUG    | app.services.browser_pool:shutdown:758 - Successfully closed browser 1
```

### Format Components

1. **Timestamp**: `2025-05-29 09:50:22.002`
   - Date and time with millisecond precision
   - Format: `YYYY-MM-DD HH:MM:SS.mmm`

2. **Log Level**: `DEBUG    `
   - Log level padded to 8 characters for alignment
   - Levels: DEBUG, INFO, WARNING, ERROR, CRITICAL

3. **Location**: `app.services.browser_pool:shutdown:758`
   - Module path, function name, and line number
   - Format: `module.path:function_name:line_number`

4. **Message**: `Successfully closed browser 1`
   - The actual log message

## Configuration

### Environment Variables

You can configure logging behavior using environment variables:

- `LOG_LEVEL`: Set the logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
- `LOG_FORMAT`: Set the log format (`viding`, `json`, `detailed`)
- `LOG_FILE`: Path to the log file (default: `logs/app.log`)
- `LOG_MAX_BYTES`: Maximum log file size before rotation (default: 10MB)
- `LOG_BACKUP_COUNT`: Number of backup log files to keep (default: 5)

### Format Options

1. **viding** (default): Custom Viding format as shown above
2. **json**: JSON format for production environments
3. **detailed**: Traditional detailed format for development

### Examples

```bash
# Use DEBUG level with Viding format
export LOG_LEVEL=DEBUG
export LOG_FORMAT=viding

# Use JSON format for production
export LOG_FORMAT=json
export LOG_LEVEL=INFO
```

## Usage in Code

### Getting a Logger

```python
from app.core.logging import get_logger

logger = get_logger(__name__)
```

### Logging Messages

```python
# Different log levels
logger.debug("Detailed debugging information")
logger.info("General information")
logger.warning("Warning message")
logger.error("Error occurred")

# Logging with exception information
try:
    # Some operation
    pass
except Exception as e:
    logger.error("Operation failed", exc_info=True)
```

### Best Practices

1. **Use appropriate log levels**:
   - `DEBUG`: Detailed information for debugging
   - `INFO`: General operational information
   - `WARNING`: Something unexpected happened but the application is still working
   - `ERROR`: A serious problem occurred

2. **Include context in messages**:
   ```python
   logger.info(f"Processing request for user {user_id}")
   logger.error(f"Failed to connect to database: {db_url}")
   ```

3. **Use structured logging for complex data**:
   ```python
   logger.info("Request completed", extra={
       'user_id': user_id,
       'duration': duration,
       'status_code': 200
   })
   ```

## Log Files

- **Location**: `logs/app.log` (configurable via `LOG_FILE`)
- **Rotation**: Automatic rotation when file reaches 10MB (configurable)
- **Retention**: Keeps 5 backup files (configurable)

## Development vs Production

### Development
- Default format: `viding`
- Log level: `DEBUG`
- Console output: Enabled
- File logging: Enabled

### Production
- Default format: `json`
- Log level: `INFO`
- Console output: Enabled (for Docker containers)
- File logging: Enabled

## Testing

The logging configuration includes comprehensive unit tests in `tests/test_viding_formatter.py` that verify:

- Correct format structure
- Timestamp precision
- Log level formatting
- Exception handling
- Different module names

Run tests with:
```bash
python -m pytest tests/test_viding_formatter.py -v
```
