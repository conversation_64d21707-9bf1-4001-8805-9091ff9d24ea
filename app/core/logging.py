"""
Logging configuration and utilities for the application.
"""

import logging
import logging.handlers
import os
import sys
import uuid
from datetime import datetime, timezone
from flask import request, g
from pythonjsonlogger import jsonlogger


class RequestIDFilter(logging.Filter):
    """Add request ID to log records for traceability"""

    def filter(self, record):
        # Try to get request ID from Flask's g object
        try:
            record.request_id = getattr(g, 'request_id', 'no-request')
        except RuntimeError:
            # Outside of request context
            record.request_id = 'no-request'
        return True


class VidingFormatter(logging.Formatter):
    """Custom formatter that matches the Viding logging format:
    2025-05-29 09:50:22.002 | DEBUG    | app.services.browser_pool:shutdown:758 - Successfully closed browser 1
    """

    def format(self, record):
        # Format timestamp with milliseconds
        dt = datetime.fromtimestamp(record.created)
        timestamp = dt.strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]  # Remove last 3 digits to get milliseconds

        # Format log level (padded to 8 characters)
        level = f"{record.levelname:<8}"

        # Format module:function:line_number
        module_path = record.name
        function_name = record.funcName if hasattr(record, 'funcName') else 'unknown'
        line_number = record.lineno if hasattr(record, 'lineno') else 0
        location = f"{module_path}:{function_name}:{line_number}"

        # Format the message
        message = record.getMessage()

        # Combine all parts
        formatted = f"{timestamp} | {level} | {location} - {message}"

        # Add exception info if present
        if record.exc_info:
            formatted += '\n' + self.formatException(record.exc_info)

        return formatted


class ContextualFormatter(logging.Formatter):
    """Custom formatter that adds contextual information"""

    def format(self, record):
        # Add timestamp
        record.timestamp = datetime.now(timezone.utc).isoformat()

        # Add request context if available
        try:
            if request:
                record.method = request.method
                record.url = request.url
                record.remote_addr = request.remote_addr
                record.user_agent = request.headers.get('User-Agent', 'Unknown')
        except RuntimeError:
            # Outside of request context
            pass

        return super().format(record)


def setup_logging(app):
    """Configure logging for the Flask application"""

    # Get configuration from app config
    log_level = app.config.get('LOG_LEVEL', 'INFO')
    log_file = app.config.get('LOG_FILE', 'logs/app.log')
    log_format = app.config.get('LOG_FORMAT', 'detailed')
    max_bytes = app.config.get('LOG_MAX_BYTES', 10 * 1024 * 1024)  # 10MB
    backup_count = app.config.get('LOG_BACKUP_COUNT', 5)

    # Detect if running in Docker/production
    is_docker = os.path.exists('/.dockerenv') or os.environ.get('DOCKER_CONTAINER', False)
    is_production = not app.config.get('DEBUG', False)

    # Create logs directory if it doesn't exist
    log_dir = os.path.dirname(log_file)
    if log_dir and not os.path.exists(log_dir):
        os.makedirs(log_dir, exist_ok=True)

    # Clear existing handlers to avoid duplicates
    app.logger.handlers.clear()

    # Also clear root logger handlers to avoid conflicts with gunicorn
    if is_docker and is_production:
        logging.getLogger().handlers.clear()

    # Set log level
    numeric_level = getattr(logging, log_level.upper(), logging.INFO)
    app.logger.setLevel(numeric_level)

    # Create formatters
    if log_format == 'json':
        # JSON formatter for production
        json_formatter = jsonlogger.JsonFormatter(
            '%(timestamp)s %(name)s %(levelname)s %(request_id)s %(message)s'
        )
        console_formatter = json_formatter
        file_formatter = json_formatter
    elif log_format == 'viding':
        # Viding custom formatter
        viding_formatter = VidingFormatter()
        console_formatter = viding_formatter
        file_formatter = viding_formatter
    else:
        # Detailed formatter for development
        detailed_format = (
            '%(asctime)s - %(name)s - %(levelname)s - '
            '[%(request_id)s] - %(funcName)s:%(lineno)d - %(message)s'
        )
        console_format = (
            '%(asctime)s - %(levelname)s - [%(request_id)s] - %(message)s'
        )

        console_formatter = ContextualFormatter(console_format)
        file_formatter = ContextualFormatter(detailed_format)

    # Add request ID filter
    request_filter = RequestIDFilter()

    # Console handler - always add for Docker/production visibility
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(numeric_level)
    console_handler.setFormatter(console_formatter)
    console_handler.addFilter(request_filter)

    # Force console output in Docker/production
    if is_docker and is_production:
        console_handler.setLevel(logging.INFO)  # Ensure INFO+ logs show in Docker

    # Configure root logger for consistent formatting across all loggers
    root_logger = logging.getLogger()
    root_logger.setLevel(numeric_level)

    # Clear existing root handlers to avoid duplicates
    root_logger.handlers.clear()

    # Add our console handler to root logger
    root_console_handler = logging.StreamHandler(sys.stdout)
    root_console_handler.setLevel(numeric_level)
    root_console_handler.setFormatter(console_formatter)
    root_console_handler.addFilter(request_filter)
    root_logger.addHandler(root_console_handler)

    # Don't add handler to app.logger to avoid duplicates
    # Instead, let it propagate to root logger
    app.logger.propagate = True

    # File handler with rotation
    if log_file:
        file_handler = logging.handlers.RotatingFileHandler(
            log_file,
            maxBytes=max_bytes,
            backupCount=backup_count
        )
        file_handler.setLevel(numeric_level)
        file_handler.setFormatter(file_formatter)
        file_handler.addFilter(request_filter)
        root_logger.addHandler(file_handler)

    # Configure other loggers
    configure_external_loggers(numeric_level)

    # Add request logging middleware
    setup_request_logging(app)

    app.logger.info("Logging configured successfully", extra={
        'log_level': log_level,
        'log_file': log_file,
        'log_format': log_format
    })


def configure_external_loggers(level):
    """Configure logging for external libraries"""

    # Requests library
    requests_logger = logging.getLogger('requests')
    requests_logger.setLevel(logging.WARNING)

    # urllib3 (used by requests)
    urllib3_logger = logging.getLogger('urllib3')
    urllib3_logger.setLevel(logging.WARNING)

    # Werkzeug (Flask's WSGI server)
    werkzeug_logger = logging.getLogger('werkzeug')
    werkzeug_logger.setLevel(logging.WARNING)

    # Gunicorn
    gunicorn_logger = logging.getLogger('gunicorn')
    gunicorn_logger.setLevel(level)


def setup_request_logging(app):
    """Setup request/response logging middleware"""

    @app.before_request
    def before_request():
        # Generate unique request ID
        g.request_id = str(uuid.uuid4())[:8]
        g.start_time = datetime.now(timezone.utc)

        # Log incoming request
        app.logger.info("Request started", extra={
            'method': request.method,
            'url': request.url,
            'remote_addr': request.remote_addr,
            'user_agent': request.headers.get('User-Agent', 'Unknown'),
            'content_length': request.content_length
        })

    @app.after_request
    def after_request(response):
        # Calculate request duration
        if hasattr(g, 'start_time'):
            duration = (datetime.now(timezone.utc) - g.start_time).total_seconds()
        else:
            duration = 0

        # Log response
        app.logger.info("Request completed", extra={
            'status_code': response.status_code,
            'content_length': response.content_length,
            'duration_seconds': duration
        })

        return response

    @app.errorhandler(Exception)
    def handle_exception(e):
        # Log unhandled exceptions
        app.logger.error("Unhandled exception occurred", extra={
            'exception_type': type(e).__name__,
            'exception_message': str(e)
        }, exc_info=True)

        # Return a generic error response
        return {
            'error': 'Internal server error',
            'request_id': getattr(g, 'request_id', 'unknown')
        }, 500


def get_logger(name):
    """Get a logger instance with proper configuration"""
    logger = logging.getLogger(name)

    # Ensure the logger inherits from root logger configuration
    # This prevents issues with logger hierarchy
    if not logger.handlers:
        logger.propagate = True

    return logger
