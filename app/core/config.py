"""
Configuration classes for different environments.
"""

import os
from typing import Type
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()


class Config:
    """Base configuration class with common settings."""

    # Flask configuration
    SECRET_KEY = os.getenv('SECRET_KEY', 'dev-secret-key-change-in-production')

    # Viding host configuration
    VIDING_HOST = os.getenv('HOST', 'viding.co')

    # Default URLs and settings
    DEFAULT_THUMBNAIL = 'https://viding.co/images/icon/viding-logo-32x32.png'
    FALLBACK_HTML_PATH = 'fallback.html'

    # Request timeout configuration
    REQUEST_TIMEOUT = int(os.getenv('REQUEST_TIMEOUT', 30))

    # Performance monitoring
    SLOW_REQUEST_THRESHOLD = float(os.getenv('SLOW_REQUEST_THRESHOLD', 2.0))

    # Logging configuration
    LOG_LEVEL = os.getenv('LOG_LEVEL', 'INFO')
    LOG_FILE = os.getenv('LOG_FILE', 'logs/app.log')
    LOG_FORMAT = os.getenv('LOG_FORMAT', 'viding')
    LOG_MAX_BYTES = int(os.getenv('LOG_MAX_BYTES', 10 * 1024 * 1024))  # 10MB
    LOG_BACKUP_COUNT = int(os.getenv('LOG_BACKUP_COUNT', 5))

    # Security headers
    SECURITY_HEADERS = {
        'X-Content-Type-Options': 'nosniff',
        'X-Frame-Options': 'SAMEORIGIN',
        'X-XSS-Protection': '1; mode=block',
        'Referrer-Policy': 'strict-origin-when-cross-origin'
    }

    # Cache configuration
    CACHE_TYPE = os.getenv('CACHE_TYPE', 'simple')
    CACHE_DEFAULT_TIMEOUT = int(os.getenv('CACHE_DEFAULT_TIMEOUT', 300))  # 5 minutes

    @staticmethod
    def init_app(app):
        """Initialize application with this configuration."""
        pass


class DevelopmentConfig(Config):
    """Development environment configuration."""

    DEBUG = True
    LOG_LEVEL = 'DEBUG'
    LOG_FORMAT = 'viding'

    # Disable security headers in development for easier debugging
    SECURITY_HEADERS = {}

    @staticmethod
    def init_app(app):
        Config.init_app(app)

        # Development-specific initialization
        app.logger.info("Running in DEVELOPMENT mode")


class ProductionConfig(Config):
    """Production environment configuration."""

    DEBUG = False
    LOG_LEVEL = os.getenv('LOG_LEVEL', 'INFO')
    LOG_FORMAT = 'json'

    # Enhanced security in production
    SECURITY_HEADERS = {
        **Config.SECURITY_HEADERS,
        'Strict-Transport-Security': 'max-age=31536000; includeSubDomains',
        'Content-Security-Policy': "default-src 'self' https:; script-src 'self' 'unsafe-inline' https:; style-src 'self' 'unsafe-inline' https:; img-src 'self' data: https:; font-src 'self' https:; connect-src 'self' https:; frame-src https:;"
    }

    @staticmethod
    def init_app(app):
        Config.init_app(app)

        # Production-specific initialization
        app.logger.info("Running in PRODUCTION mode")


class TestingConfig(Config):
    """Testing environment configuration."""

    TESTING = True
    DEBUG = True
    LOG_LEVEL = 'DEBUG'
    LOG_FORMAT = 'viding'

    # Use in-memory cache for testing
    CACHE_TYPE = 'simple'
    CACHE_DEFAULT_TIMEOUT = 1

    # Shorter timeouts for faster tests
    REQUEST_TIMEOUT = 5
    SLOW_REQUEST_THRESHOLD = 1.0

    # Disable security headers in testing
    SECURITY_HEADERS = {}

    @staticmethod
    def init_app(app):
        Config.init_app(app)

        # Testing-specific initialization
        app.logger.info("Running in TESTING mode")


# Configuration mapping
config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'testing': TestingConfig,
    'default': DevelopmentConfig
}


def get_config(config_name: str = None) -> Type[Config]:
    """Get configuration class based on environment name."""
    if config_name is None:
        config_name = os.getenv('FLASK_ENV', 'default')

    return config.get(config_name, DevelopmentConfig)
