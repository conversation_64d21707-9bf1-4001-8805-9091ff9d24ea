"""
Unit tests for the VidingFormatter logging class.
"""

import logging
import unittest
from io import String<PERSON>
from datetime import datetime

from app.core.logging import VidingFormatter


class TestVidingFormatter(unittest.TestCase):
    """Test cases for VidingFormatter."""

    def setUp(self):
        """Set up test fixtures."""
        self.formatter = VidingFormatter()
        self.stream = StringIO()
        self.handler = logging.StreamHandler(self.stream)
        self.handler.setFormatter(self.formatter)
        
        self.logger = logging.getLogger('test.module')
        self.logger.setLevel(logging.DEBUG)
        self.logger.addHandler(self.handler)

    def tearDown(self):
        """Clean up after tests."""
        self.logger.removeHandler(self.handler)
        self.handler.close()

    def test_debug_log_format(self):
        """Test DEBUG level log formatting."""
        self.logger.debug("Successfully closed browser 1")
        output = self.stream.getvalue().strip()
        
        # Check format: YYYY-MM-DD HH:MM:SS.mmm | LEVEL    | module:function:line - message
        parts = output.split(' | ')
        self.assertEqual(len(parts), 3)
        
        # Check timestamp format
        timestamp = parts[0]
        self.assertRegex(timestamp, r'\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\.\d{3}')
        
        # Check log level (padded to 8 characters)
        level = parts[1]
        self.assertEqual(level, 'DEBUG   ')
        
        # Check location and message
        location_and_message = parts[2]
        self.assertIn('test.module:test_debug_log_format:', location_and_message)
        self.assertIn('Successfully closed browser 1', location_and_message)

    def test_info_log_format(self):
        """Test INFO level log formatting."""
        self.logger.info("Browser pool initialized with 5 browsers")
        output = self.stream.getvalue().strip()
        
        parts = output.split(' | ')
        level = parts[1]
        self.assertEqual(level, 'INFO    ')
        self.assertIn('Browser pool initialized with 5 browsers', parts[2])

    def test_warning_log_format(self):
        """Test WARNING level log formatting."""
        self.logger.warning("Browser pool is running low")
        output = self.stream.getvalue().strip()
        
        parts = output.split(' | ')
        level = parts[1]
        self.assertEqual(level, 'WARNING ')
        self.assertIn('Browser pool is running low', parts[2])

    def test_error_log_format(self):
        """Test ERROR level log formatting."""
        self.logger.error("Failed to create new browser instance")
        output = self.stream.getvalue().strip()
        
        parts = output.split(' | ')
        level = parts[1]
        self.assertEqual(level, 'ERROR   ')
        self.assertIn('Failed to create new browser instance', parts[2])

    def test_exception_logging(self):
        """Test exception logging with traceback."""
        try:
            raise ValueError("Test exception")
        except Exception:
            self.logger.error("An error occurred", exc_info=True)
        
        output = self.stream.getvalue()
        self.assertIn('An error occurred', output)
        self.assertIn('Traceback', output)
        self.assertIn('ValueError: Test exception', output)

    def test_different_module_names(self):
        """Test logging with different module names."""
        # Test with different logger names
        loggers = [
            'app.services.browser_pool',
            'app.api.pages',
            'app.core.middleware',
            'app.services.content_fetcher'
        ]
        
        for logger_name in loggers:
            with self.subTest(logger_name=logger_name):
                logger = logging.getLogger(logger_name)
                logger.addHandler(self.handler)
                logger.setLevel(logging.DEBUG)
                
                # Clear the stream
                self.stream.seek(0)
                self.stream.truncate(0)
                
                logger.info(f"Test message from {logger_name}")
                output = self.stream.getvalue().strip()
                
                parts = output.split(' | ')
                location_and_message = parts[2]
                self.assertIn(logger_name, location_and_message)
                
                logger.removeHandler(self.handler)

    def test_timestamp_precision(self):
        """Test that timestamp includes milliseconds."""
        self.logger.info("Test timestamp precision")
        output = self.stream.getvalue().strip()
        
        timestamp = output.split(' | ')[0]
        # Should have format: YYYY-MM-DD HH:MM:SS.mmm
        self.assertRegex(timestamp, r'\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\.\d{3}')
        
        # Verify it's a valid datetime
        dt = datetime.strptime(timestamp, '%Y-%m-%d %H:%M:%S.%f')
        self.assertIsInstance(dt, datetime)


if __name__ == '__main__':
    unittest.main()
