#!/usr/bin/env python3
"""
Test script to demonstrate the new Viding logging format.
"""

import os
import sys

# Add the parent directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(__file__)))

from app import create_app
from app.core.logging import get_logger

def test_logging_format():
    """Test the new logging format."""

    # Create the Flask app
    app = create_app('development')

    with app.app_context():
        # Get a logger
        logger = get_logger('app.services.browser_pool')

        # Test different log levels
        logger.debug("Successfully closed browser 1")
        logger.info("Browser pool initialized with 5 browsers")
        logger.warning("Browser pool is running low on available browsers")
        logger.error("Failed to create new browser instance")

        # Test with different module names
        service_logger = get_logger('app.services.content_fetcher')
        service_logger.info("Content fetched successfully from https://example.com")

        api_logger = get_logger('app.api.pages')
        api_logger.debug("Processing request for page: /wedding/john-jane")

        # Test with exception
        try:
            raise ValueError("This is a test exception")
        except Exception as e:
            logger.error("An error occurred during processing", exc_info=True)

if __name__ == '__main__':
    test_logging_format()
